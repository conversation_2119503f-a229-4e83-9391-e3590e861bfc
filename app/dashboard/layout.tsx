import { ReactNode } from "react";
import { <PERSON><PERSON> } from "@/common/components/organisms";
import { Metada<PERSON> } from "next";
import { ProjectProvider } from "@/common/hooks/useProjects";
import { Toaster } from "@/common/components/molecules/toast/index";
import { PageViewTracker } from "@/common/components/analytics/PageViewTracker";

export const metadata: Metadata = {
  title: 'MediaPilot Dashboard',
  alternates: {
    canonical: `https://mediapilot.app/dashboard`,
  },
  openGraph: {
    title: 'MediaPilot Dashboard',
    url: `https://mediapilot.app/dashboard`,
  },
  twitter: {
    title: 'MediaPilot Dashboard',
  },
}

export default function Layout ({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <div className="relative min-h-screen pt-16">
      <PageViewTracker pageName="Dashboard" />
      <Header />
      <ProjectProvider>
        {children}
      </ProjectProvider>
      <Toaster />
    </div>
  )
}
