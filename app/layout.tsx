import {
  ReactNode,
} from "react";
import {
  primaryFont,
} from "@/common/utils/localFont";
import { metaObject } from "./metaData";
import { AnalyticsProvider } from "@/common/contexts/AnalyticsContext";
import './globals.css'

export const metadata = metaObject

export default async function RootLayout ({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <html lang="en" className={`${primaryFont.className}`}>
      <body className={`bg-eerie-black`}>
        <AnalyticsProvider>
          {children}
        </AnalyticsProvider>
      </body>
    </html>
  )
}
