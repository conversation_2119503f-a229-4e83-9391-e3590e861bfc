import { Suspense } from 'react';
import { Loader } from "@/common/components/atoms";
import dynamic from "next/dynamic";
import { Metadata } from "next";
import {
  <PERSON><PERSON>, Header,
} from '@/common/components/organisms';
import {
  OrganizationStructuredData,
  SoftwareApplicationStructuredData,
  WebSiteStructuredData,
  ServiceStructuredData,
  WebPageStructuredData,
  FAQStructuredData,
  ProductStructuredData,
} from '@/common/components/StructuredData';
import { lang } from '@/common/lang';
import { metaObject } from '@/app/metaData';
import { PageViewTracker } from "@/common/components/analytics/PageViewTracker";

export const metadata: Metadata = metaObject;

const MediaPilotPage = () => {

  const Client = dynamic(() => import('./(home)/client').then(m => m.Client), {
    ssr: false,
    loading: () => <Loader />,
  });

  return (
    <>
      <PageViewTracker pageName="Home" />
      <OrganizationStructuredData />
      <WebSiteStructuredData />
      <SoftwareApplicationStructuredData />
      <ProductStructuredData />
      <ServiceStructuredData />
      <WebPageStructuredData
        name="MediaPilot | AI-Powered Social Media Management Platform"
        description="Streamline your social media workflow with AI assistance. Create, schedule, and analyze content across multiple platforms from a single dashboard."
        url="https://mediapilot.app"
      />
      <FAQStructuredData faqs={lang.clientPage.faq.questions} />
      <div className="hidden">
        <h1>Media Pilot</h1>
      </div>
      <Suspense fallback={<Loader />}>
        <Header />
        <Client />
        <Footer />
      </Suspense>
    </>
  );
};

export default MediaPilotPage;
