'use client'

import React, { 
  createContext, useContext, useEffect, ReactNode,
  useCallback,
} from 'react';
import { usePathname } from 'next/navigation';
import { Mixpanel } from '@/common/utils/mixpanel';
import { 
  MixpanelEventName, 
  UserProperties, 
  PageViewProperties,
  ProjectProperties,
  ContentProperties,
  SocialAccountProperties,
  ErrorProperties,
} from '@/common/utils/mixpanel/types';
import { useSupabaseAuth } from '@/common/hooks';

interface AnalyticsContextType {
  // User identification
  identifyUser: (userId: string, properties?: UserProperties) => void;
  updateUserProperties: (properties: UserProperties) => void;
  resetUser: () => void;
  
  // Event tracking
  trackEvent: (eventName: MixpanelEventName, properties?: Record<string, any>) => void;
  trackPageView: (properties?: Partial<PageViewProperties>) => void;
  trackProjectEvent: (eventName: MixpanelEventName, properties: ProjectProperties) => void;
  trackContentEvent: (eventName: MixpanelEventName, properties: ContentProperties) => void;
  trackSocialAccountEvent: (eventName: MixpanelEventName, properties: SocialAccountProperties) => void;
  trackError: (error: Error | string, additionalProperties?: Partial<ErrorProperties>) => void;
  
  // Feature tracking
  trackFeatureUsage: (featureName: string, properties?: Record<string, any>) => void;
  trackModalInteraction: (modalName: string, action: 'opened' | 'closed', properties?: Record<string, any>) => void;
  
  // Utility
  isInitialized: boolean;
  getDistinctId: () => string | null;
}

const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined);

interface AnalyticsProviderProps {
  children: ReactNode;
}

export const AnalyticsProvider: React.FC<AnalyticsProviderProps> = ({ children }) => {
  const pathname = usePathname();
  const { 
    user, isAuthenticated,
  } = useSupabaseAuth();

  // Track page views automatically

  const trackPageView = useCallback((properties: Partial<PageViewProperties> = {}) => {
    const pageViewProps: PageViewProperties = {
      page: getPageName(pathname),
      path: pathname,
      referrer: typeof window !== 'undefined' ? document.referrer : undefined,
      userId: user?.id,
      ...properties,
    };
    
    Mixpanel.track(MixpanelEventName.pageView, pageViewProps);
  }, [pathname, user?.id]);


  useEffect(() => {
    if (pathname) {
      trackPageView({
        page: getPageName(pathname),
        path: pathname,
        userId: user?.id,
      });
    }
  }, [pathname, user?.id, trackPageView]);

  useEffect(() => {
    if (isAuthenticated && user) {
      identifyUser(user.id, {
        userId: user.id,
        email: user.email,
        name: user.user_metadata?.name || user.user_metadata?.full_name,
        signUpDate: user.created_at,
        lastActiveDate: new Date().toISOString(),
      });
    } else if (!isAuthenticated) {
      resetUser();
    }
  }, [isAuthenticated, user]);

  const identifyUser = (userId: string, properties?: UserProperties) => {
    Mixpanel.identify(userId);
    if (properties) {
      Mixpanel.people.setOnce({
        userId: properties.userId,
        email: properties.email,
        signUpDate: properties.signUpDate,
      });
      Mixpanel.people.set(properties);
    }
  };

  const updateUserProperties = (properties: UserProperties) => {
    Mixpanel.people.set(properties);
  };

  const resetUser = () => {
    Mixpanel.reset();
  };

  const trackEvent = (eventName: MixpanelEventName, properties: Record<string, any> = {}) => {
    const enrichedProperties = {
      ...properties,
      userId: user?.id,
      page: getPageName(pathname),
      path: pathname,
    };
    
    Mixpanel.track(eventName, enrichedProperties);
  };

  
  const trackProjectEvent = (eventName: MixpanelEventName, properties: ProjectProperties) => {
    trackEvent(eventName, properties);
  };

  const trackContentEvent = (eventName: MixpanelEventName, properties: ContentProperties) => {
    trackEvent(eventName, properties);
  };

  const trackSocialAccountEvent = (eventName: MixpanelEventName, properties: SocialAccountProperties) => {
    trackEvent(eventName, properties);
  };

  const trackError = (error: Error | string, additionalProperties: Partial<ErrorProperties> = {}) => {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorStack = typeof error === 'string' ? undefined : error.stack;
    
    const errorProperties: ErrorProperties = {
      errorType: typeof error === 'string' ? 'Custom Error' : error.constructor.name,
      errorMessage,
      errorStack,
      page: getPageName(pathname),
      userId: user?.id,
      ...additionalProperties,
    };
    
    trackEvent(MixpanelEventName.errorOccurred, errorProperties);
  };

  const trackFeatureUsage = (featureName: string, properties: Record<string, any> = {}) => {
    trackEvent(MixpanelEventName.featureUsed, {
      featureName,
      ...properties,
    });
  };

  const trackModalInteraction = (modalName: string, action: 'opened' | 'closed', properties: Record<string, any> = {}) => {
    const eventName = action === 'opened' ? MixpanelEventName.modalOpened : MixpanelEventName.modalClosed;
    trackEvent(eventName, {
      modalName,
      action,
      ...properties,
    });
  };

  const getDistinctId = () => {
    return Mixpanel.getDistinctId();
  };

  const value: AnalyticsContextType = {
    identifyUser,
    updateUserProperties,
    resetUser,
    trackEvent,
    trackPageView,
    trackProjectEvent,
    trackContentEvent,
    trackSocialAccountEvent,
    trackError,
    trackFeatureUsage,
    trackModalInteraction,
    isInitialized: Mixpanel.isInitialized(),
    getDistinctId,
  };

  return (
    <AnalyticsContext.Provider value={value}>
      {children}
    </AnalyticsContext.Provider>
  );
};

export const useAnalytics = (): AnalyticsContextType => {
  const context = useContext(AnalyticsContext);
  if (context === undefined) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
};

// Helper function to get page name from pathname
const getPageName = (pathname: string): string => {
  if (pathname === '/') {
    return 'Home';
  }
  if (pathname === '/dashboard') {
    return 'Dashboard';
  }
  if (pathname === '/dashboard/analytics') {
    return 'Analytics';
  }
  if (pathname === '/dashboard/calendar') {
    return 'Calendar';
  }
  if (pathname === '/dashboard/calendar/schedule') {
    return 'Schedule Post';
  }
  if (pathname === '/dashboard/accounts') {
    return 'Accounts';
  }
  if (pathname === '/dashboard/knowledge-base') {
    return 'Knowledge Base';
  }
  if (pathname === '/dashboard/settings') {
    return 'Settings';
  }
  if (pathname.startsWith('/payment')) {
    return 'Payment';
  }
  if (pathname === '/privacy') {
    return 'Privacy Policy';
  }
  if (pathname === '/terms') {
    return 'Terms of Service';
  }
  
  // Extract page name from pathname
  const segments = pathname.split('/').filter(Boolean);
  return segments.map(segment => 
    segment.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1),
    ).join(' '),
  ).join(' - ');
};
