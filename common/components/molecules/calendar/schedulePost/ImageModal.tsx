'use client'

import {
  useState, useRef, useEffect,
} from 'react';
import { DefaultModal } from '@/common/components/organisms';
import {
  Button, TextArea,
} from '@/common/components/atoms';
import {
  PhotoUploader, Dropdown, DropdownOption,
} from '@/common/components/molecules';
import { ImageStyles } from '@/common/constants';
import toast from 'react-hot-toast';
import autosize from 'autosize';
import { getPath } from '@/common/utils/helpers';
import { useAIAnalytics } from '@/common/hooks';
import { useSupabaseAuth } from '@/common/hooks/useSupabaseAuth';
import { useProjects } from '@/common/hooks';

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  agentId?: string;
  planId?: string;
  content?: string;
  onImageAttached: (imageUrl: string, isFromAI: boolean, file?: File) => void;
}

export const ImageModal = ({
  isOpen,
  onClose,
  agentId,
  planId,
  content = '',
  onImageAttached,
}: ImageModalProps) => {
  const [imagePrompt, setImagePrompt] = useState(content.trim() || '');
  const [selectedOption, setSelectedOption] = useState(ImageStyles[0]);
  const [currentImage, setCurrentImage] = useState('');
  const [imagePromptError, setImagePromptError] = useState('');
  const [imageGenerating, setImageGenerating] = useState(false);
  const [isUploadFromLocal, setIsUploadFromLocal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);

  const { user } = useSupabaseAuth();
  const { activeProject } = useProjects();
  const { trackAIImageGenerated } = useAIAnalytics();

  useEffect(() => {
    if (imagePromptRef?.current && isOpen) {
      autosize(imagePromptRef.current);
    }
  }, [isOpen]);

  useEffect(() => {
    setImagePrompt(content.trim() || '');
  }, [content, isOpen]);

  const handleStyleSelect = (option: DropdownOption) => {
    setSelectedOption(option as typeof selectedOption);
  };


  const handleGenerate = async () => {
    if (!imagePrompt) {
      setImagePromptError('Please enter an image prompt');
      return;
    }
    if (imagePrompt) {
      if (imagePrompt.length < 10) {
        setImagePromptError('Image prompt should be at least 10 characters');
        return;
      }
      if (imagePrompt.length > 400) {
        setImagePromptError('Image prompt should not exceed 400 characters');
        return;
      }
    }
    setImagePromptError('');
    setImageGenerating(true);

    try {
      if (!agentId) {
        throw new Error('Agent ID is required for image generation');
      }

      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/post-image-gen`;

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify({
          description: imagePrompt,
          style: selectedOption?.option,
          planId: planId || 'new-post',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to generate image: ${response.statusText}`);
      }

      const imageData = await response.json();

      if (imageData && imageData.filepath) {
        setCurrentImage(getPath(imageData.filepath))
        setIsUploadFromLocal(false);

        if (user && activeProject) {
          trackAIImageGenerated({
            prompt: imagePrompt,
            style: selectedOption?.option || 'default',
            platform: 'unknown',
            projectId: activeProject.project_id,
            userId: user.id,
          });
        }
      } else {
        throw new Error('Invalid response from image generation API');
      }
    } catch (error) {
      console.error('Error generating image:', error);
      toast.error('Failed to generate image');
    } finally {
      setImageGenerating(false);
    }
  };

  const handleAttachImage = () => {
    if (isUploadFromLocal) {
      if (selectedFile) {
        onImageAttached(currentImage, false, selectedFile);
      }
    } else {
      onImageAttached(currentImage, true);
    }
    onClose();
  };

  return (
    <DefaultModal
      isOpen={isOpen}
      maxWidth='md:max-w-[820px]'
      onClose={onClose}
    >
      <div className='px-4 sm:px-6 sm:pr-4 pr-2'>
        <h4 className="text-white font-semibold text-xl md:text-2xl">Attach Image</h4>
        <PhotoUploader
          id='image-upload'
          setValue={setCurrentImage}
          setSelectedFile={setSelectedFile}
          setIsUploadFromLocal={setIsUploadFromLocal}
          loading={imageGenerating}
          value={currentImage}
          errorMessage=''
        />
        <div className='mt-2'>
          <label htmlFor="image-prompt" className="text-white font-medium text-sm">
            Generate Using AI
            <TextArea
              ref={imagePromptRef}
              name="image-prompt"
              id="image-prompt"
              width='w-full'
              maxHeight='80px'
              placeholder='Create a relevant image to your post'
              value={imagePrompt}
              onChange={(e) => setImagePrompt(e.target.value)}
            />
          </label>
        </div>
        {imagePromptError ? <div className='text-tulip text-sm'>{imagePromptError}</div> : null}
        <Dropdown
          label="Image Style"
          options={ImageStyles}
          selectedOption={selectedOption}
          onSelect={handleStyleSelect}
          placeholder="Select a style"
          className="mt-2"
        />
        <div className='mt-4 flex flex-col sm:flex-row sm:justify-end gap-2 mb-2'>
          <Button
            size='lg'
            variant='outline'
            onClick={handleGenerate}
            disabled={imageGenerating}
            type="button"
          >
            Generate Image
          </Button>
          <Button
            size='lg'
            variant='gradient'
            disabled={!currentImage || imageGenerating}
            title={!currentImage ? 'Please upload or generate an image' : ''}
            type="button"
            onClick={handleAttachImage}
          >
            {'Attach Image to Post'}
          </Button>
        </div>
      </div>
    </DefaultModal>
  );
};

export default ImageModal;
