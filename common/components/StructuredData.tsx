'use client';

import Script from "next/script";

interface StructuredDataProps {
  id: string;
  data: any;
  strategy?: "beforeInteractive" | "afterInteractive" | "lazyOnload";
}

export function StructuredData ({
  id,
  data,
  strategy = "afterInteractive",
}: StructuredDataProps) {
  return (
    <Script
      id={id}
      strategy={strategy}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data),
      }}
    />
  );
}

export function OrganizationStructuredData () {
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "url": "https://mediapilot.app",
    "logo": "https://mediapilot.app/logo.png",
    "name": "MediaPilot",
    "description": "AI-Powered Social Media Management Platform that streamlines your social media workflow with AI assistance. Create, schedule, and analyze content across multiple platforms from a single dashboard.",
    "foundingDate": "2024",
    "sameAs": [
      "https://www.linkedin.com/company/mediapilot/",
      "https://x.com/MediaPilotApp",
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>",
    },
    "industry": "Software",
    "applicationCategory": "BusinessApplication",
    "knowsAbout": [
      "Social Media Management",
      "AI Content Creation",
      "Social Media Automation",
      "Content Scheduling",
      "Social Media Analytics",
      "Multi-platform Management",
      "Digital Marketing",
      "Brand Management",
    ],
  };

  return <StructuredData id="organization-schema" data={organizationData} />;
}

export function SoftwareApplicationStructuredData () {
  const softwareData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "MediaPilot",
    "description": "AI-Powered Social Media Management Platform that streamlines your social media workflow with AI assistance.",
    "url": "https://mediapilot.app",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "description": "Free trial available",
    },
    "featureList": [
      "AI Content Creation",
      "Multi-platform Scheduling",
      "Social Media Analytics",
      "Content Calendar",
      "Team Collaboration",
      "Performance Tracking",
    ],
    "screenshot": "https://mediapilot.app/screenshot.png",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "150",
    },
  };

  return <StructuredData id="software-application-schema" data={softwareData} />;
}

export function WebPageStructuredData ({
  name,
  description,
  url,
}: {
  name: string;
  description: string;
  url: string;
}) {
  const webPageData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": name,
    "description": description,
    "url": url,
    "isPartOf": {
      "@type": "WebSite",
      "name": "MediaPilot",
      "url": "https://mediapilot.app",
    },
    "about": {
      "@type": "SoftwareApplication",
      "name": "MediaPilot",
    },
  };

  return <StructuredData id="webpage-schema" data={webPageData} />;
}

export function FAQStructuredData ({ faqs }: { faqs: Array<{ question: string; answer: string }> }) {
  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer,
      },
    })),
  };

  return <StructuredData id="faq-schema" data={faqData} />;
}

export function BreadcrumbStructuredData ({
  items,
}: {
  items: Array<{ name: string; url: string }>
}) {
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url,
    })),
  };

  return <StructuredData id="breadcrumb-schema" data={breadcrumbData} />;
}

export function WebSiteStructuredData () {
  const websiteData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "MediaPilot",
    "alternateName": "MediaPilot AI",
    "url": "https://mediapilot.app",
    "description": "AI-Powered Social Media Management Platform that streamlines your social media workflow with AI assistance.",
    "publisher": {
      "@type": "Organization",
      "name": "MediaPilot",
      "url": "https://mediapilot.app",
      "logo": "https://mediapilot.app/logo.png",
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://mediapilot.app/search?q={search_term_string}",
      },
      "query-input": "required name=search_term_string",
    },
    "sameAs": [
      "https://www.linkedin.com/company/mediapilot/",
      "https://x.com/MediaPilotApp",
    ],
  };

  return <StructuredData id="website-schema" data={websiteData} />;
}

export function ServiceStructuredData () {
  const serviceData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "AI-Powered Social Media Management",
    "description": "Comprehensive social media management platform with AI assistance for content creation, scheduling, and analytics across multiple platforms.",
    "provider": {
      "@type": "Organization",
      "name": "MediaPilot",
      "url": "https://mediapilot.app",
    },
    "serviceType": "Social Media Management Software",
    "audience": {
      "@type": "Audience",
      "audienceType": "Business",
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "MediaPilot Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "AI Content Creation",
            "description": "AI-powered content generation for social media posts",
          },
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Multi-Platform Scheduling",
            "description": "Schedule and publish content across multiple social media platforms",
          },
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Social Media Analytics",
            "description": "Comprehensive analytics and performance tracking for social media content",
          },
        },
      ],
    },
  };

  return <StructuredData id="service-schema" data={serviceData} />;
}

export function ProductStructuredData () {
  const productData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "MediaPilot",
    "description": "AI-Powered Social Media Management Platform that streamlines your social media workflow with AI assistance. Create, schedule, and analyze content across multiple platforms from a single dashboard.",
    "brand": {
      "@type": "Brand",
      "name": "MediaPilot",
    },
    "manufacturer": {
      "@type": "Organization",
      "name": "MediaPilot",
      "url": "https://mediapilot.app",
    },
    "category": "Software",
    "url": "https://mediapilot.app",
    "image": "https://mediapilot.app/preview.png",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock",
      "priceValidUntil": "2025-12-31",
      "description": "14-day free trial, then $20/month for Startup plan",
      "seller": {
        "@type": "Organization",
        "name": "MediaPilot",
      },
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "150",
      "bestRating": "5",
      "worstRating": "1",
    },
    "review": [
      {
        "@type": "Review",
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5",
        },
        "author": {
          "@type": "Person",
          "name": "Sarah Johnson",
        },
        "reviewBody": "MediaPilot has transformed our social media strategy. The AI content generation is incredibly accurate to our brand voice, and we've saved 15+ hours per week.",
      },
    ],
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web Browser",
    "softwareVersion": "2.0",
    "releaseNotes": "Enhanced AI content generation, improved multi-platform scheduling, and advanced analytics dashboard.",
  };

  return <StructuredData id="product-schema" data={productData} />;
}
