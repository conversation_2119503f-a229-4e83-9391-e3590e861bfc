'use client'

import {
  useState, useEffect,
} from 'react';
import {
  useRouter, useSearchParams, usePathname,
} from 'next/navigation';
import {
  useProjects, useSupabaseAuth, useSocialAccountAnalytics,
} from '@/common/hooks';
import { routes } from '@/common/routes';
import toast from 'react-hot-toast';
import { Toaster } from '@/common/components/molecules';
import {
  ProjectModal, Sidebar,
} from '@/common/components/organisms';
import {
  HomeIcon,
  SettingsIcon,
  AnalyticsBoardIcon,
  CalendarIcon,
  UsersIcon,
  PlusIcon,
  KnowledgeBaseIcon,
} from '@/common/components/icons';

const navigationItems = [
  {
    id: 'overview',
    label: 'Overview',
    icon: <HomeIcon />,
    path: routes.dashboardPath,
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: <AnalyticsBoardIcon />,
    path: routes.dashboardAnalyticsPath,
  },
  {
    id: 'calendar',
    label: 'Content Calendar',
    icon: <CalendarIcon />,
    path: routes.dashboardCalendarPath,
  },
  {
    id: 'accounts',
    label: 'Social Accounts',
    icon: <UsersIcon />,
    path: routes.dashboardAccountsPath,
  },
  {
    id: 'knowledge-base',
    label: 'Knowledge Base',
    icon: <KnowledgeBaseIcon />,
    path: routes.dashboardKnowledgeBasePath,
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: <SettingsIcon />,
    path: routes.dashboardSettingsPath,
  },
];

export const DashboardLayout = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [isProjectModalOpen, setIsProjectModalOpen] = useState(false);
  const {
    user, isAuthenticated, isLoading: authLoading,
  } = useSupabaseAuth();
  const {
    projects: userProjects,
    activeProject,
    isLoading,
    fetchProjects,
    setActiveProject,
  } = useProjects();
  const router = useRouter();
  const pathname = usePathname();
  const { 
    trackAccountConnected, trackAccountConnectionFailed,
  } = useSocialAccountAnalytics();

  const searchParams = useSearchParams();

  useEffect(() => {
    const success = searchParams.get('success');
    const error = searchParams.get('error');
    const username = searchParams.get('username');

    if (success === 'account_connected') {
      toast.success('Account connected successfully');

      if (user && activeProject) {
        trackAccountConnected({
          platform: 'Unknown',
          connectionMethod: 'oauth',
          projectId: activeProject.project_id,
          userId: user.id,
        });
      }

      const url = new URL(window.location.href);
      url.searchParams.delete('success');
      window.history.replaceState({}, '', url);
    }

    if (success === 'twitter_bot_connected') {
      toast.success(`Twitter bot @${username || 'unknown'} connected successfully`);

      if (user && activeProject) {
        trackAccountConnected({
          platform: 'Twitter',
          accountName: username || undefined,
          connectionMethod: 'bot',
          projectId: activeProject.project_id,
          userId: user.id,
        });
      }

      const url = new URL(window.location.href);
      url.searchParams.delete('success');
      url.searchParams.delete('username');
      window.history.replaceState({}, '', url);
    }

    if (error) {
      toast.error('Failed to connect account');

      if (user && activeProject) {
        trackAccountConnectionFailed({
          platform: 'Unknown',
          connectionMethod: 'oauth',
          projectId: activeProject.project_id,
          userId: user.id,
          errorReason: error,
        });
      }

      const url = new URL(window.location.href);
      url.searchParams.delete('error');
      window.history.replaceState({}, '', url);
    }
  }, [searchParams, activeProject, user, trackAccountConnected, trackAccountConnectionFailed]);



  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push(routes.homePath);
    }
  }, [isAuthenticated, authLoading, router]);

  return (
    <div className="flex min-h-[calc(100vh-64px)] bg-eerie-black">
      <Sidebar
        navigationItems={navigationItems}
        currentPath={pathname}
        hasProjects={userProjects.length > 0}
        projects={userProjects}
        activeProject={activeProject}
        onSelectProject={(projectId) => {
          const project = userProjects.find(p => p.project_id === projectId);
          if (project) {
            setActiveProject(project);
          }
        }}
        onProjectCreated={() => fetchProjects(true)}
      />

      <div className="flex-1 p-6 md:p-8 overflow-auto">
        <div className="max-w-7xl mx-auto">
          {isLoading ? (
            <div className="flex items-center justify-center h-[calc(100vh-200px)]">
              <div className="animate-pulse flex flex-col items-center">
                <div className="h-12 w-12 bg-violets-are-blue/20 rounded-full mb-4"></div>
                <div className="h-4 w-48 bg-violets-are-blue/20 rounded mb-2"></div>
                <div className="h-3 w-36 bg-violets-are-blue/10 rounded"></div>
              </div>
            </div>
          ) : userProjects.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)] text-center">
              <div className="bg-violets-are-blue/10 p-6 rounded-full mb-6">
                <PlusIcon className="w-12 h-12 text-violets-are-blue" />
              </div>
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-3">No projects yet</h2>
              <p className="text-gray-400 max-w-md mb-8">Create your first project to start managing your social media presence with MediaPilot</p>
              <button
                onClick={() => setIsProjectModalOpen(true)}
                className="bg-gradient-to-tr from-han-purple to-tulip text-white px-6 py-3 rounded-xl font-medium flex items-center gap-2 hover:opacity-90 transition-opacity"
              >
                <PlusIcon className="w-5 h-5" />
                <span>Create Your First Project</span>
              </button>
            </div>
          ) : (
            <>
              {children}
            </>
          )}
        </div>
      </div>

      {user && (
        <ProjectModal
          isOpen={isProjectModalOpen}
          onClose={() => setIsProjectModalOpen(false)}
          userId={user.id}
          onProjectCreated={() => fetchProjects(true)}
        />
      )}
      <Toaster />
    </div>
  );
};
