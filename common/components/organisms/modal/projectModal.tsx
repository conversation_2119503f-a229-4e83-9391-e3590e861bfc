'use client'

import {
  useState, useEffect,
} from 'react';
import { DefaultModal } from '@/common/components/organisms';
import {
  Button,
} from '@/common/components/atoms';
import {
  Input, Toaster, FreeTrialToggle,
} from '@/common/components/molecules';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { createClient } from '@/common/utils/supabase/client';
import toast from 'react-hot-toast';
import {
  SupabaseTables, basicPlanLink,
} from '@/common/constants';
import {
  ProjectFormValues, BillingStatus,
} from '@/common/types/supabase';
import { useAnalytics } from '@/common/hooks';

const projectSchema = z.object({
  name: z.string().min(1, 'Project name is required'),
  isFreeTrial: z.boolean().optional(),
});

interface ProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  onProjectCreated: () => void;
}

export const ProjectModal = ({
  isOpen, onClose, userId, onProjectCreated,
}: ProjectModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasExistingFreeProject, setHasExistingFreeProject] = useState(false);
  const supabase = createClient();
  const {
    track, events,
  } = useAnalytics();

  const form = useForm<ProjectFormValues>({
    resolver: zodResolver(projectSchema),
    mode: 'onChange',
    defaultValues: {
      name: '',
      isFreeTrial: false,
    },
  });

  useEffect(() => {
    const checkExistingFreeProjects = async () => {
      try {
        const {
          data, error,
        } = await supabase
          .from(SupabaseTables.Projects)
          .select('*')
          .eq('user_id', userId)
          .or('billing_status.eq.free,billing_status.eq.free-ended');

        if (error) {
          console.error('Error checking free projects:', error);
          return;
        }

        setHasExistingFreeProject(data && data.length > 0);
      } catch (error) {
        console.error('Error checking free projects:', error);
      }
    };

    if (isOpen && userId) {
      checkExistingFreeProjects();
      track('Modal Opened', { modalName: 'Project Creation Modal' });
    }
  }, [isOpen, userId, supabase, track]);

  useEffect(() => {
    if (!isOpen) {
      track('Modal Closed', { modalName: 'Project Creation Modal' });
    }
  }, [isOpen, track]);

  const handleSubmit = async (data: ProjectFormValues) => {
    setIsSubmitting(true);
    try {
      const billingStatus = hasExistingFreeProject
        ? 'free-ended' as BillingStatus
        : 'free' as BillingStatus;

      const {
        data: projectData,
        error,
      } = await supabase
        .from(SupabaseTables.Projects)
        .insert([
          {
            name: data.name,
            user_id: userId,
            billing_status: billingStatus,
            accounts: JSON.stringify({
              "accounts": [
                {
                  agentId: null,
                  platform: 'Twitter',
                  connected: false,
                },
                {
                  agentId: null,
                  platform: 'LinkedIn',
                  connected: false,
                },
                {
                  agentId: null,
                  platform: 'Facebook',
                  connected: false,
                },
                {
                  agentId: null,
                  platform: 'Instagram',
                  connected: false,
                },
                {
                  agentId: null,
                  platform: 'YouTube',
                  connected: false,
                },
              ],
            },
            ),
          },
        ])
        .select()
        .single();

      if (error) {
        throw error;
      }

      const projectId = projectData.project_id;

      track(events.PROJECT_CREATED, {
        projectName: data.name,
        billingStatus,
        isFreeTrial: data.isFreeTrial,
        projectId,
        userId,
        connectedPlatforms: [],
        createdAt: new Date().toISOString(),
      });

      onClose();
      if (data.isFreeTrial) {
        toast.success('Project created successfully!');
        onProjectCreated();
      } else {
        toast.success('Redirecting to Payment!');
        const stripeUrl = `${basicPlanLink}?client_reference_id=${projectId}`;
        window.location.href = stripeUrl;
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create project';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <DefaultModal isOpen={isOpen} onClose={onClose}>
        <div className="px-6 py-2">
          <h2 className="text-2xl font-bold text-white mb-6">Add New Project</h2>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && form.formState.isValid && !isSubmitting) {
                e.preventDefault();
                form.handleSubmit(handleSubmit)();
              }
            }}
          >
            <div>
              <Input
                id="name"
                name="name"
                type="text"
                labelText="Project Name"
                placeholder="Enter project name"
                value={form.watch('name')}
                onChange={(e) => {
                  form.setValue('name', e.target.value);
                  form.trigger('name');
                }}
                error={!!form.formState.errors.name}
                errorMessage={form.formState.errors.name?.message}
                width="w-full"
              />
            </div>

            <div>
              <FreeTrialToggle
                isFreeTrial={form.watch('isFreeTrial') || false}
                onToggle={() => {
                  form.setValue('isFreeTrial', !form.watch('isFreeTrial'));
                }}
                disabled={hasExistingFreeProject}
              />
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <Button
                type="button"
                variant="outline"
                size="md"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="gradient"
                size="md"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Creating...' : 'Create Project'}
              </Button>
            </div>
          </form>
        </div>
      </DefaultModal>
      <Toaster />
    </>
  );
};
