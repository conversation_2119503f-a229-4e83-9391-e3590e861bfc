'use client'

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { analytics } from '@/common/utils/mixpanel';

interface PageViewTrackerProps {
  pageName?: string;
  properties?: Record<string, unknown>;
}

export const PageViewTracker = ({
  pageName, properties = {},
}: PageViewTrackerProps) => {
  const pathname = usePathname();

  useEffect(() => {
    const pageTitle = pageName || pathname;
    analytics.pageView(pageTitle, {
      path: pathname,
      ...properties,
    });
  }, [pathname, pageName, properties]);

  return null;
};
