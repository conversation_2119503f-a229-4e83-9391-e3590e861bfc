import { Dict } from "mixpanel-browser";
import { Mixpanel } from ".";
import {
  MixpanelEventName,
  CustomEventProps,
  UserProperties,
  ProjectProperties,
  ContentProperties,
  SocialAccountProperties,
  ErrorProperties,
} from "./types";

export type { Dict };
export { MixpanelEventName };

// Legacy hook for backward compatibility
export const useMixpanelEvent = () => {
  const mixpanelEvent = ({
    mixpanelProps = {},
    eventName,
  }: CustomEventProps) => {
    try {
      Mixpanel.track(eventName, mixpanelProps);
    } catch (err) {
      console.error("error", `Mixpanel tracking error: ${err}`);
    }
  };

  return {
    mixpanelEvent,
  };
};

// Utility functions for common tracking patterns
export const trackUserAction = (
  eventName: MixpanelEventName,
  properties: Dict = {},
) => {
  try {
    Mixpanel.track(eventName, properties);
  } catch (err) {
    console.error("Mixpanel tracking error:", err);
  }
};

export const trackProjectAction = (
  eventName: MixpanelEventName,
  properties: ProjectProperties,
) => {
  try {
    Mixpanel.track(eventName, properties);
  } catch (err) {
    console.error("Mixpanel tracking error:", err);
  }
};

export const trackContentAction = (
  eventName: MixpanelEventName,
  properties: ContentProperties,
) => {
  try {
    Mixpanel.track(eventName, properties);
  } catch (err) {
    console.error("Mixpanel tracking error:", err);
  }
};

export const trackSocialAccountAction = (
  eventName: MixpanelEventName,
  properties: SocialAccountProperties,
) => {
  try {
    Mixpanel.track(eventName, properties);
  } catch (err) {
    console.error("Mixpanel tracking error:", err);
  }
};

export const trackError = (
  error: Error | string,
  additionalProperties: Partial<ErrorProperties> = {},
) => {
  try {
    const errorMessage = typeof error === "string" ? error : error.message;
    const errorStack = typeof error === "string" ? undefined : error.stack;

    const errorProperties: ErrorProperties = {
      errorType:
        typeof error === "string" ? "Custom Error" : error.constructor.name,
      errorMessage,
      errorStack,
      page:
        typeof window !== "undefined" ? window.location.pathname : "unknown",
      ...additionalProperties,
    };

    Mixpanel.track(MixpanelEventName.errorOccurred, errorProperties);
  } catch (err) {
    console.error("Mixpanel error tracking failed:", err);
  }
};

export const identifyUser = (userId: string, properties?: UserProperties) => {
  try {
    Mixpanel.identify(userId);
    if (properties) {
      Mixpanel.people.setOnce({
        userId: properties.userId,
        email: properties.email,
        signUpDate: properties.signUpDate,
      });
      Mixpanel.people.set(properties);
    }
  } catch (err) {
    console.error("Mixpanel user identification error:", err);
  }
};

export const updateUserProperties = (properties: UserProperties) => {
  try {
    Mixpanel.people.set(properties);
  } catch (err) {
    console.error("Mixpanel user properties update error:", err);
  }
};

export const resetUser = () => {
  try {
    Mixpanel.reset();
  } catch (err) {
    console.error("Mixpanel reset error:", err);
  }
};
