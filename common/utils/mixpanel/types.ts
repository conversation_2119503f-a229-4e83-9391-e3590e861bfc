/* eslint-disable no-shadow */
import { Dict } from "mixpanel-browser";

export enum MixpanelEventName {
  // Page Navigation
  pageView = "Page View",

  // Authentication Events
  userSignUp = "User Sign Up",
  userSignIn = "User Sign In",
  userSignOut = "User Sign Out",
  passwordReset = "Password Reset",

  // Project Management
  projectCreated = "Project Created",
  projectUpdated = "Project Updated",
  projectDeleted = "Project Deleted",
  projectSelected = "Project Selected",

  // Account Management
  socialAccountConnected = "Social Account Connected",
  socialAccountDisconnected = "Social Account Disconnected",
  socialAccountConnectionFailed = "Social Account Connection Failed",

  // Content Creation
  contentGenerated = "Content Generated",
  contentEdited = "Content Edited",
  contentDeleted = "Content Deleted",
  imageGenerated = "Image Generated",
  imageUploaded = "Image Uploaded",

  // Content Scheduling
  postScheduled = "Post Scheduled",
  postUpdated = "Post Updated",
  postDeleted = "Post Deleted",
  postPublished = "Post Published",

  // AI Interactions
  aiContentImproved = "AI Content Improved",
  aiNewPostGenerated = "AI New Post Generated",
  aiImageGenerated = "AI Image Generated",

  // Knowledge Base
  documentUploaded = "Document Uploaded",
  documentDeleted = "Document Deleted",
  knowledgeBaseAccessed = "Knowledge Base Accessed",

  // Analytics & Insights
  analyticsViewed = "Analytics Viewed",
  performanceMetricsViewed = "Performance Metrics Viewed",

  // Settings & Configuration
  settingsUpdated = "Settings Updated",
  profileUpdated = "Profile Updated",
  notificationSettingsChanged = "Notification Settings Changed",

  // Payment & Subscription
  subscriptionUpgraded = "Subscription Upgraded",
  paymentCompleted = "Payment Completed",
  paymentFailed = "Payment Failed",

  // Error Events
  errorOccurred = "Error Occurred",
  apiCallFailed = "API Call Failed",

  // Feature Usage
  featureUsed = "Feature Used",
  tooltipViewed = "Tooltip Viewed",
  modalOpened = "Modal Opened",
  modalClosed = "Modal Closed",
}

export type CustomEventProps = {
  mixpanelProps: Dict;
  eventName: MixpanelEventName;
};

export type UserProperties = {
  userId?: string;
  email?: string;
  name?: string;
  signUpDate?: string;
  lastActiveDate?: string;
  projectCount?: number;
  connectedAccounts?: string[];
  subscriptionTier?: string;
  totalPostsScheduled?: number;
  totalContentGenerated?: number;
};

export type PageViewProperties = {
  page: string;
  path: string;
  referrer?: string;
  userId?: string;
  projectId?: string;
};

export type ProjectProperties = {
  projectId: string;
  projectName: string;
  connectedPlatforms: string[];
  createdAt: string;
  userId: string;
};

export type ContentProperties = {
  contentType: "text" | "image" | "video";
  platform: string;
  characterCount?: number;
  hasImage?: boolean;
  isAIGenerated?: boolean;
  projectId: string;
  userId: string;
};

export type SocialAccountProperties = {
  platform: string;
  accountId?: string;
  accountName?: string;
  connectionMethod: string;
  projectId: string;
  userId: string;
};

export type ErrorProperties = {
  errorType: string;
  errorMessage: string;
  errorStack?: string;
  page: string;
  userId?: string;
  projectId?: string;
};
