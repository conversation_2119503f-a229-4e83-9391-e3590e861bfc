import {
  isProd, mixpanelToken,
} from "@/common/constants";
import mixpanel, { Dict } from "mixpanel-browser";

export enum AnalyticsEvent {
  PAGE_VIEW = 'Page View',
  USER_SIGNUP = 'User Signup',
  USER_LOGIN = 'User Login',
  PROJECT_CREATED = 'Project Created',
  ACCOUNT_CONNECTED = 'Account Connected',
  POST_SCHEDULED = 'Post Scheduled',
  POST_GENERATED = 'Post Generated',
}

let isInitialized = false;

const initializeMixpanel = () => {
  if (!mixpanelToken || isInitialized) {
    return;
  }

  try {
    if (isProd) {
      mixpanel.init(mixpanelToken, {
        record_sessions_percent: 1,
        persistence: "localStorage",
        track_pageview: false,
        secure_cookie: true,
        ip: false,
        property_blacklist: ["$current_url", "$initial_referrer", "$referrer"],
      });
      mixpanel.start_session_recording();
    } else {
      mixpanel.init(mixpanelToken, {
        debug: true,
        persistence: "localStorage",
        track_pageview: false,
        ip: false,
        property_blacklist: ["$current_url", "$initial_referrer", "$referrer"],
      });
    }
    isInitialized = true;
  } catch (error) {
    console.error("Failed to initialize Mixpanel:", error);
  }
};

export const analytics = {
  init: initializeMixpanel,

  identify: (userId: string) => {
    if (!mixpanelToken) {
      return;
    }
    initializeMixpanel();
    try {
      mixpanel.identify(userId);
    } catch (error) {
      console.error('Analytics identify error:', error);
    }
  },

  track: (event: AnalyticsEvent | string, properties: Dict = {}) => {
    if (!mixpanelToken) {
      return;
    }
    initializeMixpanel();
    try {
      const enrichedProps = {
        ...properties,
        timestamp: new Date().toISOString(),
        environment: isProd ? "production" : "development",
        user_agent:
          typeof window !== "undefined"
            ? window.navigator.userAgent
            : undefined,
      };
      mixpanel.track(event, enrichedProps);
    } catch (error) {
      console.error('Analytics track error:', error);
    }
  },

  setUserProperties: (properties: Dict) => {
    if (!mixpanelToken) {
      return;
    }
    initializeMixpanel();
    try {
      mixpanel.people.set({
        ...properties,
        $last_seen: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Analytics setUserProperties error:', error);
    }
  },

  pageView: (pageName: string, properties: Dict = {}) => {
    analytics.track(AnalyticsEvent.PAGE_VIEW, {
      page: pageName,
      ...properties,
    });
  },

  // Additional utility methods
  reset: () => {
    if (!mixpanelToken) {
      return;
    }
    initializeMixpanel();
    try {
      mixpanel.reset();
    } catch (error) {
      console.error('Analytics reset error:', error);
    }
  },

  getDistinctId: () => {
    if (!mixpanelToken || !isInitialized) {
      return null;
    }
    try {
      return mixpanel.get_distinct_id();
    } catch (error) {
      console.error('Analytics get_distinct_id error:', error);
      return null;
    }
  },

  isInitialized: () => isInitialized,
};

// Export Mixpanel object for direct access
export const Mixpanel = {
  init: initializeMixpanel,
  identify: (userId: string) => {
    if (!mixpanelToken) {
      return;
    }
    initializeMixpanel();
    try {
      mixpanel.identify(userId);
    } catch (error) {
      console.error('Mixpanel identify error:', error);
    }
  },
  track: (event: string, properties: Dict = {}) => {
    if (!mixpanelToken) {
      return;
    }
    initializeMixpanel();
    try {
      const enrichedProps = {
        ...properties,
        timestamp: new Date().toISOString(),
        environment: isProd ? "production" : "development",
        user_agent:
          typeof window !== "undefined"
            ? window.navigator.userAgent
            : undefined,
      };
      mixpanel.track(event, enrichedProps);
    } catch (error) {
      console.error('Mixpanel track error:', error);
    }
  },
  people: {
    set: (properties: Dict) => {
      if (!mixpanelToken) {
        return;
      }
      initializeMixpanel();
      try {
        mixpanel.people.set({
          ...properties,
          $last_seen: new Date().toISOString(),
        });
      } catch (error) {
        console.error('Mixpanel people.set error:', error);
      }
    },
    setOnce: (properties: Dict) => {
      if (!mixpanelToken) {
        return;
      }
      initializeMixpanel();
      try {
        mixpanel.people.set_once(properties);
      } catch (error) {
        console.error('Mixpanel people.setOnce error:', error);
      }
    },
  },
  reset: () => {
    if (!mixpanelToken) {
      return;
    }
    initializeMixpanel();
    try {
      mixpanel.reset();
    } catch (error) {
      console.error('Mixpanel reset error:', error);
    }
  },
  getDistinctId: () => {
    if (!mixpanelToken || !isInitialized) {
      return null;
    }
    try {
      return mixpanel.get_distinct_id();
    } catch (error) {
      console.error('Mixpanel get_distinct_id error:', error);
      return null;
    }
  },
  isInitialized: () => isInitialized,
};
