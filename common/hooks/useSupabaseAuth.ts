'use client'

import {
  useCallback, useEffect, useState,
} from 'react';
import { createClient } from '@/common/utils/supabase/client';
import {
  Session, User, AuthError,
} from '@supabase/supabase-js';
import toast from 'react-hot-toast';
import {
  useRouter, useSearchParams, usePathname,
} from 'next/navigation';
import { routes } from '@/common/routes';
import {
  analytics, AnalyticsEvent,
} from '@/common/utils/mixpanel';

export type SignUpCredentials = {
  email: string;
  password: string;
  name?: string;
};

export type SignInCredentials = {
  email: string;
  password: string;
};

export const useSupabaseAuth = () => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isAuthenticating, setIsAuthenticating] = useState<boolean>(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const supabase = createClient();

  useEffect(() => {
    if (searchParams.has('auth_provider') || searchParams.has('code')) {
      if (searchParams.get('auth_provider') === 'google') {
        const params = new URLSearchParams(searchParams.toString());
        params.delete('auth_provider');
        params.delete('code');
        params.delete('provider_token');
        params.delete('provider_refresh_token');
        params.delete('type');
        const newSearch = params.toString();
        const newUrl = pathname + (newSearch ? `?${newSearch}` : '');
        router.replace(newUrl, { scroll: false });

        router.push(routes.dashboardPath);
      } else {
        const params = new URLSearchParams(searchParams.toString());
        params.delete('auth_provider');
        params.delete('code');
        params.delete('provider_token');
        params.delete('provider_refresh_token');
        params.delete('type');
        const newSearch = params.toString();
        const newUrl = pathname + (newSearch ? `?${newSearch}` : '');
        router.replace(newUrl, { scroll: false });
      }
    }
  }, [searchParams, pathname, router]);

  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);

      try {
        const { data: { session: currentSession } } = await supabase.auth.getSession();
        setSession(currentSession);
        setUser(currentSession?.user || null);

        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (event, newSession) => {
            setSession(newSession);
            setUser(newSession?.user || null);

            if (newSession?.user) {
              analytics.identify(newSession.user.id);
              analytics.setUserProperties({
                email: newSession.user.email,
                name: newSession.user.user_metadata?.name,
                provider: newSession.user.app_metadata?.provider,
              });
            }
          },
        );

        return () => {
          subscription.unsubscribe();
        };
      } catch (error) {
        toast.error('Error authenticating the account');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, [supabase.auth, router, searchParams]);

  const signInWithGoogle = useCallback(async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}?auth_provider=google`,
        },
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      toast.error('Error signing in with Google');
      throw error;
    }
  }, [supabase.auth]);

  const signUpWithEmail = useCallback(async ({
    email, password, name,
  }: SignUpCredentials) => {
    setIsAuthenticating(true);
    try {
      const {
        data, error,
      } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
          emailRedirectTo: `${window.location.origin}?auth_provider=email`,
        },
      });

      if (error) {
        throw error;
      }

      if (data?.user) {
        toast.success('Account created successfully! Please check your email for verification.');
        analytics.track(AnalyticsEvent.USER_SIGNUP, {
          email: data.user.email,
          provider: 'email',
        });
        return data;
      }
    } catch (error) {
      const authError = error as AuthError;
      toast.error(authError.message || 'Error creating account');
      throw error;
    } finally {
      setIsAuthenticating(false);
    }
  }, [supabase.auth]);

  const signInWithEmail = useCallback(async ({
    email, password,
  }: SignInCredentials) => {
    setIsAuthenticating(true);
    try {
      const {
        data, error,
      } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      if (data?.user) {
        toast.success('Logged in successfully!');
        analytics.track(AnalyticsEvent.USER_LOGIN, {
          email: data.user.email,
          provider: 'email',
        });
        return data;
      }
    } catch (error) {
      const authError = error as AuthError;
      toast.error(authError.message || 'Invalid login credentials');
      throw error;
    } finally {
      setIsAuthenticating(false);
    }
  }, [supabase.auth]);

  const resetPassword = useCallback(async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}?type=recovery`,
      });

      if (error) {
        throw error;
      }

      toast.success('Password reset email sent!');
    } catch (error) {
      const authError = error as AuthError;
      toast.error(authError.message || 'Error sending password reset email');
      throw error;
    }
  }, [supabase.auth]);

  const updatePassword = useCallback(async (password: string, suppressToast = false) => {
    setIsAuthenticating(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password,
      });

      if (error) {
        throw error;
      }

      if (!suppressToast) {
        toast.success('Password updated successfully!');
      }
      return true;
    } catch (error) {
      const authError = error as AuthError;
      toast.error(authError.message || 'Error updating password');
      throw error;
    } finally {
      setIsAuthenticating(false);
    }
  }, [supabase.auth]);

  const signOut = useCallback(async () => {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }
    } catch (error) {
      toast.error('Unable to sign out');
      throw error;
    }
  }, [supabase.auth]);

  return {
    session,
    user,
    isAuthenticated: !!user,
    isLoading,
    isAuthenticating,
    signInWithGoogle,
    signUpWithEmail,
    signInWithEmail,
    resetPassword,
    updatePassword,
    signOut,
  };
};
