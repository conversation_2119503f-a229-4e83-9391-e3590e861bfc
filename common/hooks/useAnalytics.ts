'use client'

import { useCallback } from 'react';
import {
  analytics, AnalyticsEvent,
} from '@/common/utils/mixpanel';
import { Dict } from 'mixpanel-browser';

export const useAnalytics = () => {
  const track = useCallback((event: AnalyticsEvent | string, properties?: Dict) => {
    analytics.track(event, properties);
  }, []);

  const identify = useCallback((userId: string) => {
    analytics.identify(userId);
  }, []);

  const setUserProperties = useCallback((properties: Dict) => {
    analytics.setUserProperties(properties);
  }, []);

  const pageView = useCallback((pageName: string, properties?: Dict) => {
    analytics.pageView(pageName, properties);
  }, []);

  return {
    track,
    identify,
    setUserProperties,
    pageView,
    events: AnalyticsEvent,
  };
};

// Social Account Analytics Hook
export const useSocialAccountAnalytics = () => {
  const trackAccountConnected = useCallback((properties: Dict) => {
    analytics.track(AnalyticsEvent.ACCOUNT_CONNECTED, properties);
  }, []);

  const trackAccountConnectionFailed = useCallback((properties: Dict) => {
    analytics.track('Account Connection Failed', properties);
  }, []);

  const trackAccountDisconnected = useCallback((properties: Dict) => {
    analytics.track('Account Disconnected', properties);
  }, []);

  return {
    trackAccountConnected,
    trackAccountConnectionFailed,
    trackAccountDisconnected,
  };
};

// AI Analytics Hook
export const useAIAnalytics = () => {
  const trackAIContentImproved = useCallback((properties: Dict) => {
    analytics.track('AI Content Improved', properties);
  }, []);

  const trackAINewPostGenerated = useCallback((properties: Dict) => {
    analytics.track(AnalyticsEvent.POST_GENERATED, properties);
  }, []);

  const trackAIImageGenerated = useCallback((properties: Dict) => {
    analytics.track('AI Image Generated', properties);
  }, []);

  return {
    trackAIContentImproved,
    trackAINewPostGenerated,
    trackAIImageGenerated,
  };
};
